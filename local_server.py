#!/usr/bin/env python3
"""
Local HTTP server để test Lambda function như một API thực sự
"""
import sys
import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# Thê<PERSON> thư mục hello_world vào Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'hello_world'))

from app import lambda_handler

class LambdaHandler(BaseHTTPRequestHandler):
    """HTTP Request Handler mô phỏng API Gateway"""
    
    def do_GET(self):
        """Xử lý GET requests"""
        self.handle_request('GET')
    
    def do_POST(self):
        """Xử lý POST requests"""
        self.handle_request('POST')
    
    def handle_request(self, method):
        """Xử lý request và gọi Lambda function"""
        try:
            # Parse URL
            parsed_url = urlparse(self.path)
            
            # Chỉ xử lý /hello endpoint
            if parsed_url.path != '/hello':
                self.send_error(404, "Not Found")
                return
            
            # <PERSON><PERSON><PERSON> body nếu có
            content_length = int(self.headers.get('Content-Length', 0))
            body = None
            if content_length > 0:
                body = self.rfile.read(content_length).decode('utf-8')
            
            # Tạo mock event giống API Gateway
            mock_event = {
                "httpMethod": method,
                "path": parsed_url.path,
                "queryStringParameters": dict(parse_qs(parsed_url.query)) if parsed_url.query else None,
                "headers": dict(self.headers),
                "body": body,
                "isBase64Encoded": False,
                "requestContext": {
                    "httpMethod": method,
                    "path": parsed_url.path,
                    "stage": "local"
                }
            }
            
            # Gọi Lambda function
            response = lambda_handler(mock_event, None)
            
            # Gửi response
            self.send_response(response.get('statusCode', 200))
            
            # Set headers
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            # Gửi body
            response_body = response.get('body', '{}')
            self.wfile.write(response_body.encode('utf-8'))
            
            # Log request
            print(f"✅ {method} {self.path} -> {response.get('statusCode', 200)}")
            
        except Exception as e:
            print(f"❌ Error handling request: {str(e)}")
            self.send_error(500, f"Internal Server Error: {str(e)}")
    
    def do_OPTIONS(self):
        """Xử lý CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override để custom log format"""
        pass  # Tắt default logging

def run_server(port=3000):
    """Chạy local server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, LambdaHandler)
    
    print("=" * 60)
    print("🚀 LOCAL LAMBDA SERVER STARTED")
    print("=" * 60)
    print(f"📡 Server running on: http://localhost:{port}")
    print(f"🎯 API endpoint: http://localhost:{port}/hello")
    print("💡 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Local Lambda Server')
    parser.add_argument('--port', type=int, default=3000, help='Port to run server on (default: 3000)')
    args = parser.parse_args()
    
    run_server(args.port)
