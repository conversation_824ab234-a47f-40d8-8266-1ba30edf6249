#!/usr/bin/env python3
"""
Script để test Lambda function local
"""
import sys
import os
import json

# Thê<PERSON> thư mục hello_world vào Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'hello_world'))

from app import lambda_handler

def test_lambda_local():
    """Test Lambda function với mock event"""
    
    # Mock event giống như API Gateway gửi
    mock_event = {
        "httpMethod": "GET",
        "path": "/hello",
        "queryStringParameters": None,
        "headers": {
            "Accept": "application/json",
            "User-Agent": "test-client"
        },
        "body": None,
        "isBase64Encoded": False,
        "requestContext": {
            "httpMethod": "GET",
            "path": "/hello"
        }
    }
    
    # Mock context (có thể để None cho test đơn giản)
    mock_context = None
    
    print("🚀 Testing Lambda function locally...")
    print(f"📥 Input event: {json.dumps(mock_event, indent=2)}")
    print("-" * 50)
    
    try:
        # Gọi Lambda function
        response = lambda_handler(mock_event, mock_context)
        
        print("✅ Lambda function executed successfully!")
        print(f"📤 Response: {json.dumps(response, indent=2)}")
        
        # Parse body để hiển thị đẹp hơn
        if 'body' in response:
            body = json.loads(response['body'])
            print(f"📝 Message: {body.get('message', 'No message')}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 LOCAL LAMBDA FUNCTION TEST")
    print("=" * 60)
    
    success = test_lambda_local()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Test completed successfully!")
        print("💡 Your Lambda function is working correctly!")
    else:
        print("❌ Test failed!")
    print("=" * 60)
